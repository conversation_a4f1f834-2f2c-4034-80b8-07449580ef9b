package com.partner.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.partner.dto.base.CountryStateCityParamsDto;
import com.partner.dto.offeritem.MAppDefatStudentOfferItemDto;
import com.partner.dto.student.MStudentParamsDetailDto;
import com.partner.dto.student.MStudentParamsDto;
import com.partner.dto.student.MStudentSubAndAddOrEditDto;
import com.partner.dto.student.paramsmapper.MStudentParams;
import com.partner.dto.student.paramsmapper.MStudentParamsDetail;
import com.partner.entity.MStudentEntity;
import com.partner.vo.base.CountryBaseCombox;
import com.partner.vo.offeritem.MAppAppalyStudentOfferItemVo;
import com.partner.vo.student.*;
import com.partner.vo.combox.StudentOfferItemCourtyCombox;
import com.partner.vo.combox.StudentOfferItemStepCombox;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student】的数据库操作Mapper
* @createDate 2024-12-05 19:03:19
* @Entity com.partner.entity.MStudent
*/
@Mapper
@DS("saledb")
public interface MStudentMapper extends BaseMapper<MStudentEntity> {


    List<StudentOfferItemCourtyCombox> getCountryComboxPeople(MStudentParams params);
    List<StudentOfferItemCourtyCombox> getCountryComboxApply(MStudentParams params);



    List<StudentOfferItemStepCombox> getAllStepOrderApplyNums(MStudentParams params);

    List<StudentOfferItemStepCombox> getAllPeopleNum(MStudentParams params);


    List<MStudentStepList> getPeopleStudentList(MStudentParams params);
    List<MStudentStepList> getApplyStudentList(MStudentParams params);

    List<MAppAppalyStudentOfferItemVo> getAppOfferItemStudents(MAppDefatStudentOfferItemDto params);

    List<MStudentHomeVo> getHomeStudents(MStudentParams params);

    MStudentDetailTmpVo getStudentInfo( @Param("studentId") Long studentId);



    int getPeopleStudentNum(MStudentParams params);



    int getPeopleStudentApplayTotal(MStudentParams params);

    List<StudentMonthOfferVo> getMonthApplayTotal(MStudentParams params);

    List<StudentMonthOfferVo> getMonthsubApplyTotal(MStudentParams params);




    List<StudentApplyRankingVo> getStudentsApplyInstitutionRanking(MStudentParams params);


    StudentOfferItemDetailVo getApplyDetail(MStudentParamsDetail params);



    int getMonthNewSum(MStudentParams params);
    int finshMonthNum(MStudentParams params);

    List<CountryBaseCombox> getCountryCombox(MStudentParams params);


    MStudentEntity selectAgentOne(MStudentSubAndAddOrEditDto dto);

    // ============================= 优化版本查询方法 =============================
    
    /**
     * 优化版本：两阶段查询 - 第一阶段：获取学生UUID列表（去重+分页）
     * @param page 分页参数
     * @param params 查询参数
     * @return 学生UUID分页结果
     */
    IPage<String> selectStudentUUIDPageForPeople(Page<String> page, @Param("params") MStudentParams params);

    /**
     * 优化版本：第二阶段：根据学生UUID列表获取详细信息
     * @param studentUUIDs 学生UUID列表
     * @return 学生详细信息列表
     */
    List<MStudentCoreInfoVO> selectStudentDetailsByUUIDs(@Param("studentUUIDs") List<String> studentUUIDs);
    
    
    /**
     * 批量查询学校信息
     * @param institutionIds 学校ID列表
     * @return 学校信息列表
     */
    List<InstitutionInfoVO> batchGetInstitutions(@Param("institutionIds") List<Long> institutionIds);
    
    /**
     * 批量查询国家信息
     * @param countryIds 国家ID列表
     * @return 国家信息列表
     */
    List<CountryInfoVO> batchGetCountries(@Param("countryIds") List<Long> countryIds);
    
    /**
     * 批量查询Partner用户信息
     * @param studentIds 学生ID列表
     * @return Partner用户信息列表
     */
    List<PartnerUserInfoVO> batchGetPartnerUsers(@Param("studentIds") List<Long> studentIds);

    /**
     * 批量查询课程信息
     * @param courseIds 课程ID列表
     * @return 课程信息列表
     */
    List<CourseInfoVO> batchGetCourses(@Param("courseIds") List<Long> courseIds);

}




