<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.partner.mapper.MStudentMapper">

    <resultMap id="BaseResultMap" type="com.partner.entity.MStudentEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
        <result property="fkClientId" column="fk_client_id" jdbcType="BIGINT"/>
        <result property="num" column="num" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
        <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
        <result property="fkAreaCountryIdNationality" column="fk_area_country_id_nationality" jdbcType="BIGINT"/>
        <result property="fkAreaCountryNameNationality" column="fk_area_country_name_nationality" jdbcType="VARCHAR"/>
        <result property="fkAreaCountryIdGreenCard" column="fk_area_country_id_green_card" jdbcType="BIGINT"/>
        <result property="passportNum" column="passport_num" jdbcType="VARCHAR"/>
        <result property="fkAreaCountryIdPassport" column="fk_area_country_id_passport" jdbcType="BIGINT"/>
        <result property="fkAreaCountryIdBirth" column="fk_area_country_id_birth" jdbcType="BIGINT"/>
        <result property="fkAreaStateIdBirth" column="fk_area_state_id_birth" jdbcType="BIGINT"/>
        <result property="fkAreaCityIdBirth" column="fk_area_city_id_birth" jdbcType="BIGINT"/>
        <result property="fkAreaCountryNameBirth" column="fk_area_country_name_birth" jdbcType="VARCHAR"/>
        <result property="fkAreaStateNameBirth" column="fk_area_state_name_birth" jdbcType="VARCHAR"/>
        <result property="fkAreaCityNameBirth" column="fk_area_city_name_birth" jdbcType="VARCHAR"/>
        <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="telAreaCode" column="tel_area_code" jdbcType="VARCHAR"/>
        <result property="tel" column="tel" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
        <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
        <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
        <result property="fkAreaCountryName" column="fk_area_country_name" jdbcType="VARCHAR"/>
        <result property="fkAreaStateName" column="fk_area_state_name" jdbcType="VARCHAR"/>
        <result property="fkAreaCityName" column="fk_area_city_name" jdbcType="VARCHAR"/>
        <result property="zipcode" column="zipcode" jdbcType="VARCHAR"/>
        <result property="contactAddress" column="contact_address" jdbcType="VARCHAR"/>
        <result property="educationLevelType" column="education_level_type" jdbcType="VARCHAR"/>
        <result property="educationMajor" column="education_major" jdbcType="VARCHAR"/>
        <result property="fkAreaCountryIdEducation" column="fk_area_country_id_education" jdbcType="BIGINT"/>
        <result property="fkAreaStateIdEducation" column="fk_area_state_id_education" jdbcType="BIGINT"/>
        <result property="fkAreaCityIdEducation" column="fk_area_city_id_education" jdbcType="BIGINT"/>
        <result property="fkAreaCountryNameEducation" column="fk_area_country_name_education" jdbcType="VARCHAR"/>
        <result property="fkAreaStateNameEducation" column="fk_area_state_name_education" jdbcType="VARCHAR"/>
        <result property="fkAreaCityNameEducation" column="fk_area_city_name_education" jdbcType="VARCHAR"/>
        <result property="fkInstitutionIdEducation" column="fk_institution_id_education" jdbcType="BIGINT"/>
        <result property="fkInstitutionNameEducation" column="fk_institution_name_education" jdbcType="VARCHAR"/>
        <result property="institutionTypeEducation" column="institution_type_education" jdbcType="VARCHAR"/>
        <result property="educationLevelType2" column="education_level_type2" jdbcType="VARCHAR"/>
        <result property="educationMajor2" column="education_major2" jdbcType="VARCHAR"/>
        <result property="fkAreaCountryIdEducation2" column="fk_area_country_id_education2" jdbcType="BIGINT"/>
        <result property="fkAreaStateIdEducation2" column="fk_area_state_id_education2" jdbcType="BIGINT"/>
        <result property="fkAreaCityIdEducation2" column="fk_area_city_id_education2" jdbcType="BIGINT"/>
        <result property="fkAreaCountryNameEducation2" column="fk_area_country_name_education2" jdbcType="VARCHAR"/>
        <result property="fkAreaStateNameEducation2" column="fk_area_state_name_education2" jdbcType="VARCHAR"/>
        <result property="fkAreaCityNameEducation2" column="fk_area_city_name_education2" jdbcType="VARCHAR"/>
        <result property="fkInstitutionIdEducation2" column="fk_institution_id_education2" jdbcType="BIGINT"/>
        <result property="fkInstitutionNameEducation2" column="fk_institution_name_education2" jdbcType="VARCHAR"/>
        <result property="educationProject" column="education_project" jdbcType="INTEGER"/>
        <result property="educationDegree" column="education_degree" jdbcType="INTEGER"/>
        <result property="isComplexEducation" column="is_complex_education" jdbcType="BIT"/>
        <result property="complexEducationRemark" column="complex_education_remark" jdbcType="VARCHAR"/>
        <result property="highSchoolTestType" column="high_school_test_type" jdbcType="VARCHAR"/>
        <result property="highSchoolTestScore" column="high_school_test_score" jdbcType="VARCHAR"/>
        <result property="standardTestType" column="standard_test_type" jdbcType="VARCHAR"/>
        <result property="standardTestScore" column="standard_test_score" jdbcType="VARCHAR"/>
        <result property="masterTestType" column="master_test_type" jdbcType="VARCHAR"/>
        <result property="masterTestScore" column="master_test_score" jdbcType="VARCHAR"/>
        <result property="englishTestType" column="english_test_type" jdbcType="VARCHAR"/>
        <result property="englishTestScore" column="english_test_score" jdbcType="DECIMAL"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="receivedApplicationDataDate" column="received_application_data_date" jdbcType="TIMESTAMP"/>
        <result property="sharedPath" column="shared_path" jdbcType="VARCHAR"/>
        <result property="conditionType" column="condition_type" jdbcType="VARCHAR"/>
        <result property="numGea" column="num_gea" jdbcType="VARCHAR"/>
        <result property="numIae" column="num_iae" jdbcType="VARCHAR"/>
        <result property="idIssue" column="id_issue" jdbcType="VARCHAR"/>
        <result property="idIssueInfo" column="id_issue_info" jdbcType="VARCHAR"/>
        <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
        <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


    <select id="getCountryComboxPeople" resultType="com.partner.vo.combox.StudentOfferItemCourtyCombox">
        SELECT a.fk_area_country_id AS countryId,
               uAreaCountry.`name`,
               uAreaCountry.name_chn,
               COUNT(a.fk_student_id) AS studentCount
        FROM (SELECT offerItem.fk_student_id, offerItem.fk_area_country_id
              FROM ais_sale_center.m_student_offer_item offerItem
                    <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
                    INNER JOIN ais_sale_center.m_student student ON offerItem.fk_student_id = student.id
                    INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                    LEFT  JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
              WHERE 1=1
                <if test="year != 0 ">
                    AND YEAR(offerItem.gmt_create)=#{year}
                </if>
                <if test="studentName!=null and studentName!= '' ">
                    AND
                    (
                    student.name   like concat('%',#{studentName},'%')
                    OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') like concat('%',#{studentName},'%')
                    OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') like concat('%',#{studentName},'%')
                    OR institution.name like concat('%',#{studentName},'%')
                    OR institution.name_chn like concat('%',#{studentName},'%')
                    OR institutionCourse.name like concat('%',#{studentName},'%')
                    )

                </if>
              GROUP BY offerItem.fk_student_id, offerItem.fk_area_country_id
              ) a
        LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON a.fk_area_country_id = uAreaCountry.id
        GROUP BY a.fk_area_country_id, uAreaCountry.`name`, uAreaCountry.name_chn, uAreaCountry.view_order
        ORDER BY uAreaCountry.view_order DESC
    </select>
    <select id="getCountryComboxApply" resultType="com.partner.vo.combox.StudentOfferItemCourtyCombox">
        SELECT
            a.fk_area_country_id as countryId,
            uAreaCountry.`name`,
            uAreaCountry.name_chn,
            uAreaCountry.view_order,
            COUNT(a.id) student_count
        FROM (
        SELECT offerItem.id, offerItem.fk_area_country_id FROM ais_sale_center.m_student_offer_item offerItem
                <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
                INNER JOIN ais_sale_center.m_student student on offerItem.fk_student_id = student.id
                INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                LEFT  JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                WHERE 1=1
                <if test="year != 0 ">
                    AND YEAR(offerItem.gmt_create)=#{year}
                </if>
                <if test="studentName!=null and studentName!= '' ">
                    AND
                    (
                    student.name   like concat('%',#{studentName},'%')
                    OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') like concat('%',#{studentName},'%')
                    OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') like concat('%',#{studentName},'%')
                    OR institution.name like concat('%',#{studentName},'%')
                    OR institution.name_chn like concat('%',#{studentName},'%')
                    OR institutionCourse.name like concat('%',#{studentName},'%')
                    )
                </if>
                GROUP BY offerItem.id, offerItem.fk_area_country_id
        ) a
        LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON a.fk_area_country_id = uAreaCountry.id
        GROUP BY a.fk_area_country_id, uAreaCountry.`name`, uAreaCountry.name_chn, uAreaCountry.view_order
        ORDER BY uAreaCountry.view_order DESC
    </select>

    <select id="getAllStepOrderApplyNums" resultType="com.partner.vo.combox.StudentOfferItemStepCombox">
        SELECT
            offerItem.fk_student_offer_item_step_id AS studentOfferItemStepId,
            offerItemStep.step_order AS stepOrder,
            offerItemStep.step_name AS stepName,
            count(*) as applayCount
        FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN ais_sale_center.m_student student ON offerItem.fk_student_id = student.id
        INNER JOIN ais_sale_center.u_student_offer_item_step AS offerItemStep  ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
        INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
        LEFT  JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
        WHERE 1=1
        <if test="countryId != null ">
            AND offerItem.fk_area_country_id=#{countryId}
        </if>
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        <if test="studentName!=null and studentName!= '' ">
            AND
            (
            student.name   like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') like concat('%',#{studentName},'%')
            OR institution.name like concat('%',#{studentName},'%')
            OR institution.name_chn like concat('%',#{studentName},'%')
            OR institutionCourse.name like concat('%',#{studentName},'%')
            )
        </if>
        GROUP BY offerItem.fk_student_offer_item_step_id,offerItemStep.step_order,offerItemStep.step_name ORDER BY offerItemStep.step_order

    </select>


    <select id="getPeopleStudentList" resultType="com.partner.vo.student.MStudentStepList">
        SELECT studentUuid.fk_student_uuid                    AS studentUUID,
               student.name                  AS studentName,
               ''       AS followName,
               offerItem.fk_student_offer_id AS studentOfferId,
               offerItem.fk_institution_id   AS institutionId,
               institution.name                  AS institutionNameChn,
               institution.name_chn              AS institutionName,
               offerItemStep.id                AS stepid,
               offerItemStep.step_order        AS stepOrder,
               offerItemStep.step_name,
               institutionCourse.name AS courseName,
               uAreaCountry.name_chn AS countryName,
               (
                    SELECT GROUP_CONCAT(mPartnerUser.name) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                    LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                    where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                ) AS partnerUserName,
                (
                SELECT GROUP_CONCAT(ifnull(mPartnerUser.name_en,mPartnerUser.name)) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                )   AS partnerUserNameEn
        from ais_sale_center.m_student_offer_item offerItem
                 <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
                 INNER JOIN ais_sale_center.m_student student ON offerItem.fk_student_id = student.id
                 INNER JOIN ais_sale_center.r_student_uuid studentUuid ON studentUuid.fk_student_id=student.id
                 /*INNER JOIN ais_sale_center.r_student_agent rStudentAgent ON rStudentAgent.fk_student_id = student.id*/
                 INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                 INNER JOIN ais_sale_center.u_student_offer_item_step  offerItemStep ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
                 LEFT  JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                 LEFT  JOIN  ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id

        WHERE 1=1
        <if test="countryId != null ">
            and offerItem.fk_area_country_id=#{countryId}
        </if>
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        <if test="studentName!=null and studentName!= '' ">
            AND
            (
            student.name   like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') like concat('%',#{studentName},'%')
            OR institution.name like concat('%',#{studentName},'%')
            OR institution.name_chn like concat('%',#{studentName},'%')
            OR institutionCourse.name like concat('%',#{studentName},'%')
            )
        </if>
        ORDER BY student.id DESC,offerItemStep.step_order ASC

    </select>
    <select id="getApplyStudentList" resultType="com.partner.vo.student.MStudentStepList">

        SELECT studentUuid.fk_student_uuid                    AS studentUUID,
               student.name                  AS studentName,
               ''       AS followName,
               offerItemUUID.fk_student_offer_item_uuid                  AS offerItemUUID,
               offerItem.fk_student_offer_id AS studentOfferId,
               offerItem.fk_institution_id   AS institutionId,
               offerItem.student_offer_item_step_time AS itemStepTime,
               institution.name                  AS institutionNameChn,
               institution.name_chn              AS institutionName,
               offerItemStep.id                AS stepid,
               offerItemStep.step_order        AS stepOrder,
               offerItemStep.step_name,
               institutionCourse.name AS courseName,
               uAreaCountry.name_chn AS countryName,
                (
                SELECT GROUP_CONCAT(mPartnerUser.name) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                ) AS partnerUserName,
                (
                SELECT GROUP_CONCAT(ifnull(mPartnerUser.name_en,mPartnerUser.name)) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                )   AS partnerUserNameEn
        FROM  ais_sale_center.m_student_offer_item offerItem
                 <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
                 INNER JOIN ais_sale_center.m_student student ON student.id=offerItem.fk_student_id
                 INNER JOIN ais_sale_center.r_student_uuid studentUuid ON studentUuid.fk_student_id=student.id
                 INNER JOIN ais_sale_center.r_student_offer_item_uuid offerItemUUID ON offerItemUUID.fk_student_offer_item_id=offerItem.id
                 /*INNER JOIN ais_sale_center.r_student_agent rStudentAgent ON rStudentAgent.fk_student_id = student.id*/
                 INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                 INNER JOIN ais_sale_center.u_student_offer_item_step  offerItemStep ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
                 LEFT JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                 LEFT  JOIN  ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id


        WHERE 1=1
        <if test="countryId != null ">
            AND offerItem.fk_area_country_id=#{countryId}
        </if>
        <if test="studentOfferItemStepId != null ">
            AND offerItem.fk_student_offer_item_step_id= #{studentOfferItemStepId}
        </if>
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        <if test="studentName!=null and studentName!= '' ">
            AND
            (
            student.name   like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.last_name,''),IFNULL(student.first_name,'')),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(IFNULL(student.first_name,''),IFNULL(student.last_name,'')),' ','') like concat('%',#{studentName},'%')
            OR institution.name like concat('%',#{studentName},'%')
            OR institution.name_chn like concat('%',#{studentName},'%')
            OR institutionCourse.name like concat('%',#{studentName},'%')
            )
        </if>
        <if test="studentId != null ">
            AND  student.id=#{studentId}
        </if>
        ORDER BY student.id DESC,offerItemStep.step_order ASC
    </select>


    <select id="getStudentInfo" resultType="com.partner.vo.student.MStudentDetailTmpVo">
        SELECT student.*,
               (
                   SELECT GROUP_CONCAT(mPartnerUser.name) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                                                                    LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                   where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
               ) AS partnerUserName,
               (
                   SELECT GROUP_CONCAT(ifnull(mPartnerUser.name_en,mPartnerUser.name)) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                                                                       LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                   where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
               )   AS partnerUserNameEn,
               uAreaCountry.name  AS nationalityName,
               CASE WHEN uAreaCountryPassport.name_chn IS NULL THEN uAreaCountryPassport.name ELSE uAreaCountryPassport.name_chn END    AS areaCountryNamePassport,
               uStudentEducationLevelType.type_name_chn AS domesticEducationName,
               mAppStudent.id AS fkAppStudentId

        FROM ais_sale_center.m_student student
               LEFT  JOIN  ais_institution_center.u_area_country uAreaCountry ON student.fk_area_country_id_nationality = uAreaCountry.id
               LEFT  JOIN  ais_institution_center.u_area_country uAreaCountryPassport ON student.fk_area_country_id_passport = uAreaCountryPassport.id
               LEFT  JOIN  ais_sale_center.u_student_education_level_type uStudentEducationLevelType ON uStudentEducationLevelType.id=student.education_level_type
               LEFT  JOIN  ais_sale_center.m_app_student mAppStudent  ON mAppStudent.fk_student_id=student.id
        WHERE student.id=#{studentId} LIMIT 1
    </select>
    <select id="getPeopleStudentNum" resultType="java.lang.Integer">
        SELECT COUNT(*) total FROM (
           SELECT student.id   FROM ais_sale_center.m_student_offer_item offerItem
            <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
            INNER JOIN ais_sale_center.m_student student ON student.id=offerItem.fk_student_id
        WHERE 1=1
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
           GROUP BY student.id) a

    </select>



    <select id="getPeopleStudentApplayTotal" resultType="java.lang.Integer">
        SELECT COUNT(*) total FROM (
            SELECT offerItem.fk_student_id, offerItem.fk_area_country_id
            FROM ais_sale_center.m_student_offer_item offerItem
                 <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
            WHERE 1=1
            <if test="year != 0 ">
                AND YEAR(offerItem.gmt_create)=#{year}
            </if>
            GROUP BY offerItem.fk_student_id, offerItem.fk_area_country_id
        ) a
    </select>
    <select id="getStudentsApplyInstitutionRanking" resultType="com.partner.vo.student.StudentApplyRankingVo">
        SELECT
                offerItem.fk_institution_id   AS institutionId,
                institution.name                  AS institutionNameChn,
                institution.name_chn              AS institutionName,
                COUNT(*)                 AS applyTotal
        FROM  ais_sale_center.m_student_offer_item offerItem
              <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
              INNER JOIN ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
        WHERE 1=1
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        GROUP BY offerItem.fk_institution_id,institution.name,institution.name_chn     ORDER BY applyTotal DESC
        LIMIT 10
    </select>

    <select id="getMonthApplayTotal" resultType="com.partner.vo.student.StudentMonthOfferVo">
        SELECT  MONTH(offerItem.gmt_create) as month,COUNT(*) AS total FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        WHERE 1=1
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        GROUP BY  MONTH(offerItem.gmt_create)

    </select>

    <select id="getMonthsubApplyTotal" resultType="com.partner.vo.student.StudentMonthOfferVo">
        SELECT  MONTH(a.gmt_create) as month,COUNT(*) AS total FROM ais_sale_center.m_student_offer_item offerItem
        INNER JOIN
        (
        SELECT  offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
        WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10) GROUP BY offerItem.id
        ) a  ON  a.itemId=offerItem.id
        WHERE 1=1 AND offerItem.fk_student_offer_item_step_id  IN(6,7,8,10)
        <if test="year != 0 ">
            AND YEAR(a.gmt_create)=#{year}
        </if>
        <if test="month != 0 ">
            AND MONTH(a.gmt_create)=#{month}
        </if>
        GROUP BY  MONTH(a.gmt_create)

    </select>

    <select id="getApplyDetail" resultType="com.partner.vo.student.StudentOfferItemDetailVo">
        SELECT student.name,
               '' AS followName,
               uAreaCountry.name_chn AS areaCountryName,
               institution.name                  AS institutionName,
               institution.name_chn              AS institutionNameChn,
               institutionProvider.name AS institutionProviderName,
               institutionProvider.name_chn AS institutionProviderNameChn,
               institutionCourse.name AS courseName,
               offerItem.duration_type durationType,
               offerItem.duration,
               offerItem.submit_app_time AS newSppOptTime,
               offerItem.opening_time AS openingTime  ,
               offerItem.is_defer_entrance AS isDeferEntrance ,
               offerItem.accept_offer_deadline AS acceptOfferDeadline,
               offerItem.closing_time AS closingTime,
               offerItem.deposit_deadline  AS depositDeadline ,
               offerItem.deposit_time AS depositTime,
               offerItem.new_app_status AS  newAppStatus  ,
               offerItem.app_method AS appMethod ,
               (
                    SELECT GROUP_CONCAT(mPartnerUser.name) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                    LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                    where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                ) AS partnerUserName,
                (
                    SELECT GROUP_CONCAT(ifnull(mPartnerUser.name_en,mPartnerUser.name)) FROM  app_partner_center.r_partner_user_student rPartnerUserStudent
                    LEFT  JOIN  app_partner_center.m_partner_user mPartnerUser ON rPartnerUserStudent.fk_partner_user_id=mPartnerUser.id
                    where  rPartnerUserStudent.fk_student_id=student.id AND rPartnerUserStudent.is_active=1
                )   AS partnerUserNameEn,
               (SELECT GROUP_CONCAT(uCourseType.type_name) type_name
                FROM ais_institution_center.r_institution_course_type AS rInstitutionCourseType
                INNER JOIN ais_institution_center.u_course_type AS uCourseType ON uCourseType.id = rInstitutionCourseType.fk_course_type_id
                WHERE rInstitutionCourseType.fk_institution_course_id=institutionCourse.id   ) AS courseTypeName,
               (SELECT GROUP_CONCAT(uMajorLevel.level_name) level_name
            FROM  ais_institution_center.r_institution_course_major_level AS rInstitutionCourseMajorLevel
            INNER JOIN ais_institution_center.u_major_level AS uMajorLevel ON uMajorLevel.id = rInstitutionCourseMajorLevel.fk_major_level_id
            WHERE  rInstitutionCourseMajorLevel.fk_institution_course_id =institutionCourse.id   ) AS institutionCourseMajorLevelName

        FROM
            ais_sale_center.m_student_offer_item offerItem
                <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
                INNER  JOIN ais_sale_center.m_student student ON offerItem.fk_student_id = student.id
                INNER  JOIN  ais_institution_center.m_institution institution ON institution.id = offerItem.fk_institution_id
                LEFT  JOIN  ais_institution_center.m_institution_provider institutionProvider ON offerItem.fk_institution_provider_id=institutionProvider.id
                LEFT  JOIN  ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id
                LEFT  JOIN ais_institution_center.m_institution_course  institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id

        WHERE 1=1
          AND offerItem.id=#{offerItemId}
        LIMIT 1



    </select>
    <select id="getHomeStudents" resultType="com.partner.vo.student.MStudentHomeVo">
        SELECT
        studentUuid.fk_student_uuid                    AS studentUUID,
        student.name                  AS studentName,
        offerItemUUID.fk_student_offer_item_uuid                  AS offerItemUUID,
        offerItemStep.id                AS stepid,
        offerItemStep.step_name


        FROM
        ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN ais_sale_center.m_student student ON student.id=offerItem.fk_student_id
        INNER JOIN ais_sale_center.r_student_uuid studentUuid ON studentUuid.fk_student_id=student.id
        INNER JOIN ais_sale_center.r_student_offer_item_uuid offerItemUUID ON offerItemUUID.fk_student_offer_item_id=offerItem.id
        INNER JOIN ais_sale_center.u_student_offer_item_step  offerItemStep ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        ORDER BY offerItem.gmt_modified DESC  LIMIT 6

    </select>

    <select id="getAllPeopleNum" resultType="com.partner.vo.combox.StudentOfferItemStepCombox">

        SELECT
        offerItemStep.step_order AS stepOrder,
        offerItemStep.step_name AS stepName,
        count(*) as applayCount
        FROM ais_sale_center.u_student_offer_item_step AS offerItemStep
        INNER JOIN

        (SELECT  student.id,MAX(offerItemStep.step_order) AS maxStepOrder
        FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN ais_sale_center.m_student student ON student.id=offerItem.fk_student_id
        INNER JOIN ais_sale_center.u_student_offer_item_step AS offerItemStep  ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
        WHERE 1=1
            <if test="countryId != null ">
                AND offerItem.fk_area_country_id=#{countryId}
            </if>
            <if test="year != 0 ">
                AND YEAR(offerItem.gmt_create)=#{year}
            </if>
            <if test="month != 0 ">
                AND MONTH(offerItem.gmt_create)=#{month}
            </if>

        GROUP BY   student.id
        ) a ON offerItemStep.step_order = a.maxStepOrder GROUP BY offerItemStep.step_order




    </select>

    <select id="getMonthNewSum" resultType="java.lang.Integer">
        SELECT count(*) AS monthNew FROM


        (SELECT  fk_student_id FROM m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        <if test="year != 0 ">
            AND YEAR(offerItem.gmt_create)=#{year}
        </if>
        <if test="month != 0 ">
            AND MONTH(offerItem.gmt_create)=#{month}
        </if>
        GROUP BY fk_student_id
        ) a
    </select>

    <select id="finshMonthNum" resultType="java.lang.Integer">
        SELECT count(*) FROM
        (
        SELECT  offerItem.fk_student_id AS studentId,min(rStudentOfferItemStep.gmt_create) AS gmt_create FROM ais_sale_center.m_student_offer_item offerItem
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
        WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10) GROUP BY offerItem.fk_student_id
        ) a
        WHERE 1=1
            AND YEAR(a.gmt_create)=#{year}
            AND MONTH(a.gmt_create)=#{month}


    </select>
    <select id="getCountryCombox" resultType="com.partner.vo.base.CountryBaseCombox">
        SELECT uAreaCountry.id AS areaCountryId,uAreaCountry.name_chn AS areaCountryName FROM ais_institution_center.u_area_country uAreaCountry
        <if test="roleCode!=null and roleCode!='' and (roleCode=='COUNSELOR' || roleCode=='COPYWRINTING') ">
            INNER JOIN (
                SELECT DISTINCT fk_area_country_id FROM app_partner_center.r_partner_user_area_country WHERE fk_partner_user_id IN(#{partnerUserId}) <!--#自己Id-->
            ) areaCountry ON uAreaCountry.id = areaCountry.fk_area_country_id
        </if>
        WHERE FIND_IN_SET(13, uAreaCountry.public_level)
        ORDER BY uAreaCountry.view_order DESC
    </select>
    <select id="getAppOfferItemStudents" resultType="com.partner.vo.offeritem.MAppAppalyStudentOfferItemVo">
        SELECT mAppStudentOfferItem.*,
               institution.name                  AS institutionNameChn,
               institution.name_chn              AS institutionName,
               institutionCourse.name AS courseName
        FROM ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
           LEFT JOIN ais_institution_center.m_institution institution ON institution.id = mAppStudentOfferItem.fk_institution_id
           LEFT JOIN ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=mAppStudentOfferItem.fk_institution_course_id

        WHERE mAppStudentOfferItem.fk_app_student_id=#{fkAppStudentId} AND mAppStudentOfferItem.is_additional=1
          AND   mAppStudentOfferItem.status_additional IN (-1,0,1)



    </select>
    <select id="selectAgentOne" resultType="com.partner.entity.MStudentEntity">
        SELECT mStudent.* FROM ais_sale_center.m_student mStudent
            INNER JOIN ais_sale_center.r_student_agent rStudentAgent ON mStudent.id=rStudentAgent.fk_student_id
        WHERE 1=1
            <if test="name!=null and name!='' ">
                AND mStudent.name=#{name}
            </if>

            <if test="mobile!=null and mobile!='' ">
                AND mStudent.mobile=#{mobile}
            </if>
            <if test="email!=null and email!='' ">
                AND mStudent.email=#{email}
            </if>

            <if test="birthday!=null ">
                AND mStudent.birthday=#{birthday}
            </if>

            <if test="birthday!=null ">
                AND rStudentAgent.fk_agent_id=#{fkAgentId}
            </if>


            limit 1;

    </select>

    <!-- ============================= 优化版本SQL查询 ============================= -->
    
    <!-- 优化版本：学生核心信息分页查询 - 人员列表 -->
    <select id="selectStudentCorePageForPeople" resultType="com.partner.vo.student.MStudentCoreInfoVO">
        SELECT
            soi.id as offerItemId,
            soi.fk_student_id as studentId,
            uuid.fk_student_uuid as studentUUID,
            s.name as studentName,
            soi.fk_student_offer_id as studentOfferId,
            soi.fk_institution_id as institutionId,
            soi.fk_area_country_id as countryId,
            soi.fk_student_offer_item_step_id as stepId,
            step.step_name as stepName,
            step.step_order as stepOrder,
            soi.fk_institution_course_id as courseId
        FROM ais_sale_center.m_student_offer_item soi
        <include refid="com.partner.mapper.PermissionSqlMapper.offerItemPermissionRoleSql"/>
        INNER JOIN ais_sale_center.m_student s ON s.id = soi.fk_student_id
        INNER JOIN ais_sale_center.r_student_uuid uuid ON uuid.fk_student_id = s.id
        INNER JOIN ais_sale_center.u_student_offer_item_step step ON step.id = soi.fk_student_offer_item_step_id
        WHERE 1=1
        <if test="params.countryId != null">
            AND soi.fk_area_country_id = #{params.countryId}
        </if>
        <if test="params.year != 0">
            AND YEAR(soi.gmt_create) = #{params.year}
        </if>
        <if test="params.studentName != null and params.studentName != ''">
            AND (
                s.name LIKE CONCAT('%', #{params.studentName}, '%')
                OR REPLACE(CONCAT(IFNULL(s.last_name,''), IFNULL(s.first_name,'')), ' ', '') LIKE CONCAT('%', #{params.studentName}, '%')
                OR REPLACE(CONCAT(IFNULL(s.first_name,''), IFNULL(s.last_name,'')), ' ', '') LIKE CONCAT('%', #{params.studentName}, '%')
            )
        </if>
        ORDER BY s.id DESC, step.step_order ASC
    </select>
    
    <!-- 批量查询学校信息 -->
    <select id="batchGetInstitutions" resultType="com.partner.vo.student.InstitutionInfoVO">
        SELECT 
            i.id as institutionId,
            i.name as name,
            i.name_chn as nameChn,
            ic.id as courseId,
            ic.name as courseName
        FROM ais_institution_center.m_institution i
        LEFT JOIN ais_institution_center.m_institution_course ic ON ic.fk_institution_id = i.id
        WHERE i.id IN
        <foreach collection="institutionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <!-- 批量查询国家信息 -->
    <select id="batchGetCountries" resultType="com.partner.vo.student.CountryInfoVO">
        SELECT 
            id as countryId,
            name as name,
            name_chn as nameChn
        FROM ais_institution_center.u_area_country
        WHERE id IN
        <foreach collection="countryIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <!-- 批量查询Partner用户信息 -->
    <select id="batchGetPartnerUsers" resultType="com.partner.vo.student.PartnerUserInfoVO">
        SELECT 
            rps.fk_student_id as studentId,
            rps.fk_partner_user_id as partnerUserId,
            pu.name as name,
            IFNULL(pu.name_en, pu.name) as nameEn
        FROM app_partner_center.r_partner_user_student rps
        LEFT JOIN app_partner_center.m_partner_user pu ON rps.fk_partner_user_id = pu.id
        WHERE rps.fk_student_id IN
        <foreach collection="studentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND rps.is_active = 1
    </select>


</mapper>
